import random
import string
import json
import hashlib
import uuid
from playwright.sync_api import sync_playwright
import time
from datetime import datetime, timedelta

class AdvancedFingerprinter:
    def __init__(self):
        self.device_profiles = self._load_device_profiles()
        self.browser_versions = self._get_latest_browser_versions()

    def _load_device_profiles(self):
        """विभिन्न डिवाइस प्रोफाइल्स लोड करें"""
        return {
            'desktop': {
                'windows_10': {
                    'os': 'Windows NT 10.0; Win64; x64',
                    'platform': 'Win32',
                    'resolutions': [
                        {'width': 1920, 'height': 1080, 'ratio': 1.0},
                        {'width': 2560, 'height': 1440, 'ratio': 1.0},
                        {'width': 3840, 'height': 2160, 'ratio': 1.0},
                        {'width': 1366, 'height': 768, 'ratio': 1.0},
                        {'width': 1536, 'height': 864, 'ratio': 1.25},
                        {'width': 1600, 'height': 900, 'ratio': 1.0},
                        {'width': 1440, 'height': 900, 'ratio': 1.0},
                        {'width': 2048, 'height': 1152, 'ratio': 1.0}
                    ],
                    'memory': [4, 8, 16, 32],
                    'cores': [2, 4, 6, 8, 12, 16]
                },
                'windows_11': {
                    'os': 'Windows NT 10.0; Win64; x64',
                    'platform': 'Win32',
                    'resolutions': [
                        {'width': 1920, 'height': 1080, 'ratio': 1.0},
                        {'width': 2560, 'height': 1440, 'ratio': 1.0},
                        {'width': 3840, 'height': 2160, 'ratio': 1.0},
                        {'width': 1366, 'height': 768, 'ratio': 1.0},
                        {'width': 2560, 'height': 1600, 'ratio': 1.0}
                    ],
                    'memory': [8, 16, 32, 64],
                    'cores': [4, 6, 8, 12, 16, 20]
                },
                'macos_monterey': {
                    'os': 'Macintosh; Intel Mac OS X 10_15_7',
                    'platform': 'MacIntel',
                    'resolutions': [
                        {'width': 1440, 'height': 900, 'ratio': 2.0},
                        {'width': 1680, 'height': 1050, 'ratio': 2.0},
                        {'width': 1920, 'height': 1200, 'ratio': 2.0},
                        {'width': 2560, 'height': 1600, 'ratio': 2.0},
                        {'width': 2880, 'height': 1800, 'ratio': 2.0}
                    ],
                    'memory': [8, 16, 32, 64],
                    'cores': [4, 6, 8, 10, 12]
                },
                'macos_ventura': {
                    'os': 'Macintosh; Intel Mac OS X 10_15_7',
                    'platform': 'MacIntel',
                    'resolutions': [
                        {'width': 1512, 'height': 982, 'ratio': 2.0},
                        {'width': 1728, 'height': 1117, 'ratio': 2.0},
                        {'width': 2056, 'height': 1329, 'ratio': 2.0}
                    ],
                    'memory': [8, 16, 32, 64, 128],
                    'cores': [8, 10, 12, 16, 20]
                },
                'ubuntu_22': {
                    'os': 'X11; Linux x86_64',
                    'platform': 'Linux x86_64',
                    'resolutions': [
                        {'width': 1920, 'height': 1080, 'ratio': 1.0},
                        {'width': 1366, 'height': 768, 'ratio': 1.0},
                        {'width': 2560, 'height': 1440, 'ratio': 1.0},
                        {'width': 1600, 'height': 900, 'ratio': 1.0}
                    ],
                    'memory': [4, 8, 16, 32],
                    'cores': [2, 4, 6, 8, 12, 16]
                }
            },
            'mobile': {
                'iphone_14_pro': {
                    'os': 'iPhone; CPU iPhone OS 16_6 like Mac OS X',
                    'platform': 'iPhone',
                    'resolutions': [{'width': 393, 'height': 852, 'ratio': 3.0}],
                    'memory': [6],
                    'cores': [6]
                },
                'iphone_15_pro': {
                    'os': 'iPhone; CPU iPhone OS 17_0 like Mac OS X',
                    'platform': 'iPhone',
                    'resolutions': [{'width': 393, 'height': 852, 'ratio': 3.0}],
                    'memory': [8],
                    'cores': [6]
                },
                'samsung_s23': {
                    'os': 'Linux; Android 13; SM-S911B',
                    'platform': 'Linux armv8l',
                    'resolutions': [{'width': 360, 'height': 780, 'ratio': 3.0}],
                    'memory': [8, 12],
                    'cores': [8]
                },
                'pixel_7': {
                    'os': 'Linux; Android 13; Pixel 7',
                    'platform': 'Linux armv8l',
                    'resolutions': [{'width': 412, 'height': 915, 'ratio': 2.625}],
                    'memory': [8],
                    'cores': [8]
                },
                'oneplus_11': {
                    'os': 'Linux; Android 13; CPH2449',
                    'platform': 'Linux armv8l',
                    'resolutions': [{'width': 412, 'height': 919, 'ratio': 3.0}],
                    'memory': [8, 12, 16],
                    'cores': [8]
                }
            },
            'tablet': {
                'ipad_pro_12': {
                    'os': 'OS 16_6 like Mac OS X',
                    'platform': 'iPad',
                    'resolutions': [{'width': 1024, 'height': 1366, 'ratio': 2.0}],
                    'memory': [8, 16],
                    'cores': [8]
                },
                'surface_pro_9': {
                    'os': 'Windows NT 10.0; Win64; x64',
                    'platform': 'Win32',
                    'resolutions': [{'width': 1368, 'height': 912, 'ratio': 1.5}],
                    'memory': [8, 16, 32],
                    'cores': [4, 8, 12]
                }
            }
        }

    def _get_latest_browser_versions(self):
        """नवीनतम ब्राउज़र वर्जन प्राप्त करें"""
        return {
            'chrome': {
                'stable': ['120.0.6099.109', '120.0.6099.110', '120.0.6099.129', '121.0.6167.85', '121.0.6167.139'],
                'beta': ['121.0.6167.85', '122.0.6261.29'],
                'dev': ['122.0.6261.29', '123.0.6312.4']
            },
            'firefox': {
                'stable': ['121.0', '121.0.1', '122.0', '122.0.1'],
                'beta': ['122.0b9', '123.0b1'],
                'nightly': ['123.0a1']
            },
            'safari': {
                'stable': ['17.2.1', '17.3', '17.3.1'],
                'technology_preview': ['17.4']
            },
            'edge': {
                'stable': ['120.0.2210.133', '121.0.2277.83', '121.0.2277.98'],
                'beta': ['122.0.2365.8'],
                'dev': ['123.0.2420.5']
            }
        }

    def generate_user_agent(self, device_type='desktop', browser='chrome'):
        """उन्नत यूजर एजेंट जेनरेट करें"""
        device_profile = random.choice(list(self.device_profiles[device_type].values()))

        if browser == 'chrome':
            version = random.choice(self.browser_versions['chrome']['stable'])
            webkit_version = f"537.{random.randint(30, 40)}"

            if 'iPhone' in device_profile['os']:
                return f"Mozilla/5.0 ({device_profile['os']}) AppleWebKit/{webkit_version} (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/{webkit_version}"
            elif 'Android' in device_profile['os']:
                return f"Mozilla/5.0 (Linux; Android {random.randint(10, 13)}; {device_profile['os'].split(';')[-1].strip()}) AppleWebKit/{webkit_version} (KHTML, like Gecko) Chrome/{version} Mobile Safari/{webkit_version}"
            else:
                return f"Mozilla/5.0 ({device_profile['os']}) AppleWebKit/{webkit_version} (KHTML, like Gecko) Chrome/{version} Safari/{webkit_version}"

        elif browser == 'firefox':
            version = random.choice(self.browser_versions['firefox']['stable'])
            gecko_version = f"{random.randint(20100101, 20231231)}"

            if 'iPhone' in device_profile['os']:
                return f"Mozilla/5.0 ({device_profile['os']}) Gecko/{gecko_version} Firefox/{version}"
            elif 'Android' in device_profile['os']:
                return f"Mozilla/5.0 (Mobile; rv:{version}) Gecko/{version} Firefox/{version}"
            else:
                return f"Mozilla/5.0 ({device_profile['os']}; rv:{version}) Gecko/{gecko_version} Firefox/{version}"

        elif browser == 'safari':
            version = random.choice(self.browser_versions['safari']['stable'])
            webkit_version = f"605.1.{random.randint(10, 20)}"

            if 'iPhone' in device_profile['os']:
                return f"Mozilla/5.0 ({device_profile['os']}) AppleWebKit/{webkit_version} (KHTML, like Gecko) Version/{version} Mobile/15E148 Safari/{webkit_version}"
            elif 'Mac' in device_profile['os']:
                return f"Mozilla/5.0 ({device_profile['os']}) AppleWebKit/{webkit_version} (KHTML, like Gecko) Version/{version} Safari/{webkit_version}"

        return self.generate_user_agent(device_type, 'chrome')  # fallback

    def get_device_profile(self, device_type='random'):
        """डिवाइस प्रोफाइल प्राप्त करें"""
        if device_type == 'random':
            device_type = random.choice(['desktop', 'mobile', 'tablet'])

        device_profiles = self.device_profiles[device_type]
        device_name = random.choice(list(device_profiles.keys()))
        profile = device_profiles[device_name]

        resolution = random.choice(profile['resolutions'])
        memory = random.choice(profile['memory'])
        cores = random.choice(profile['cores'])

        return {
            'type': device_type,
            'name': device_name,
            'os': profile['os'],
            'platform': profile['platform'],
            'resolution': resolution,
            'memory': memory,
            'cores': cores
        }

    def generate_canvas_fingerprint(self):
        """कैनवास फिंगरप्रिंट जेनरेट करें"""
        canvas_data = {
            'text_rendering': random.choice(['subpixel', 'optimizeSpeed', 'optimizeQuality', 'geometricPrecision']),
            'font_smoothing': random.choice(['auto', 'never', 'always']),
            'webgl_vendor': random.choice(['Google Inc.', 'Mozilla', 'Apple Inc.', 'Microsoft Corporation']),
            'webgl_renderer': random.choice([
                'ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)',
                'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0, D3D11)',
                'ANGLE (AMD, AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0, D3D11)',
                'Apple GPU',
                'Mali-G78 MP14'
            ])
        }
        return canvas_data

    def generate_audio_fingerprint(self):
        """ऑडियो फिंगरप्रिंट जेनरेट करें"""
        return {
            'sample_rate': random.choice([44100, 48000, 96000]),
            'buffer_size': random.choice([128, 256, 512, 1024]),
            'channels': random.choice([1, 2, 6, 8]),
            'bit_depth': random.choice([16, 24, 32])
        }

    def generate_webrtc_fingerprint(self):
        """WebRTC फिंगरप्रिंट जेनरेट करें"""
        return {
            'local_ip': f"192.168.{random.randint(1, 255)}.{random.randint(1, 255)}",
            'public_ip': f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}",
            'ice_candidates': random.randint(2, 8),
            'dtls_fingerprint': hashlib.sha256(str(random.random()).encode()).hexdigest()[:32]
        }

    def generate_timezone_data(self):
        """टाइमज़ोन डेटा जेनरेट करें"""
        timezones = [
            {'id': 'America/New_York', 'offset': -5, 'dst': True},
            {'id': 'America/Los_Angeles', 'offset': -8, 'dst': True},
            {'id': 'Europe/London', 'offset': 0, 'dst': True},
            {'id': 'Europe/Berlin', 'offset': 1, 'dst': True},
            {'id': 'Asia/Tokyo', 'offset': 9, 'dst': False},
            {'id': 'Asia/Shanghai', 'offset': 8, 'dst': False},
            {'id': 'Asia/Kolkata', 'offset': 5.5, 'dst': False},
            {'id': 'Australia/Sydney', 'offset': 11, 'dst': True},
            {'id': 'America/Chicago', 'offset': -6, 'dst': True},
            {'id': 'Europe/Paris', 'offset': 1, 'dst': True},
            {'id': 'Asia/Dubai', 'offset': 4, 'dst': False},
            {'id': 'America/Toronto', 'offset': -5, 'dst': True}
        ]
        return random.choice(timezones)

    def generate_language_settings(self):
        """भाषा सेटिंग्स जेनरेट करें"""
        languages = [
            {'primary': 'en-US', 'accept': 'en-US,en;q=0.9'},
            {'primary': 'en-GB', 'accept': 'en-GB,en;q=0.9'},
            {'primary': 'en-IN', 'accept': 'en-IN,en;q=0.9,hi;q=0.8'},
            {'primary': 'es-ES', 'accept': 'es-ES,es;q=0.9,en;q=0.8'},
            {'primary': 'fr-FR', 'accept': 'fr-FR,fr;q=0.9,en;q=0.8'},
            {'primary': 'de-DE', 'accept': 'de-DE,de;q=0.9,en;q=0.8'},
            {'primary': 'ja-JP', 'accept': 'ja-JP,ja;q=0.9,en;q=0.8'},
            {'primary': 'zh-CN', 'accept': 'zh-CN,zh;q=0.9,en;q=0.8'},
            {'primary': 'pt-BR', 'accept': 'pt-BR,pt;q=0.9,en;q=0.8'},
            {'primary': 'ru-RU', 'accept': 'ru-RU,ru;q=0.9,en;q=0.8'}
        ]
        return random.choice(languages)

    def generate_hardware_fingerprint(self, device_profile):
        """हार्डवेयर फिंगरप्रिंट जेनरेट करें"""
        return {
            'cpu_cores': device_profile['cores'],
            'memory_gb': device_profile['memory'],
            'gpu_vendor': random.choice(['NVIDIA', 'AMD', 'Intel', 'Apple', 'Mali', 'Adreno']),
            'storage_type': random.choice(['SSD', 'HDD', 'NVMe', 'eMMC']),
            'battery_level': random.randint(20, 100) if device_profile['type'] in ['mobile', 'tablet'] else None,
            'charging': random.choice([True, False]) if device_profile['type'] in ['mobile', 'tablet'] else None
        }

    def generate_network_fingerprint(self):
        """नेटवर्क फिंगरप्रिंट जेनरेट करें"""
        connection_types = ['wifi', 'ethernet', 'cellular', '5g', '4g', '3g']
        if random.choice([True, False]):  # Mobile device
            connection = random.choice(['wifi', 'cellular', '5g', '4g'])
        else:  # Desktop
            connection = random.choice(['wifi', 'ethernet'])

        return {
            'connection_type': connection,
            'downlink': random.uniform(1.0, 100.0),
            'rtt': random.randint(10, 200),
            'effective_type': random.choice(['slow-2g', '2g', '3g', '4g']),
            'save_data': random.choice([True, False])
        }

    def generate_browser_plugins(self, device_profile):
        """ब्राउज़र प्लगइन्स जेनरेट करें"""
        common_plugins = [
            'Chrome PDF Plugin',
            'Chrome PDF Viewer',
            'Native Client',
            'Widevine Content Decryption Module'
        ]

        if device_profile['type'] == 'desktop':
            desktop_plugins = [
                'Adobe Flash Player',
                'Java Deployment Toolkit',
                'Microsoft Silverlight',
                'VLC Web Plugin',
                'QuickTime Plug-in'
            ]
            plugins = common_plugins + random.sample(desktop_plugins, random.randint(0, 3))
        else:
            plugins = common_plugins

        return plugins

    def generate_fonts_list(self, device_profile):
        """फॉन्ट्स लिस्ट जेनरेट करें"""
        common_fonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
            'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
            'Trebuchet MS', 'Arial Black', 'Impact'
        ]

        if 'Windows' in device_profile['os']:
            windows_fonts = [
                'Segoe UI', 'Calibri', 'Cambria', 'Consolas', 'Corbel',
                'Franklin Gothic Medium', 'Lucida Console', 'Lucida Sans Unicode',
                'Microsoft Sans Serif', 'Tahoma'
            ]
            fonts = common_fonts + windows_fonts
        elif 'Mac' in device_profile['os'] or 'iPhone' in device_profile['os']:
            mac_fonts = [
                'San Francisco', 'Helvetica Neue', 'Lucida Grande', 'Monaco',
                'Menlo', 'Avenir', 'Optima', 'Futura', 'Gill Sans'
            ]
            fonts = common_fonts + mac_fonts
        else:  # Linux/Android
            linux_fonts = [
                'Ubuntu', 'Liberation Sans', 'DejaVu Sans', 'Noto Sans',
                'Roboto', 'Droid Sans', 'Open Sans'
            ]
            fonts = common_fonts + linux_fonts

        return random.sample(fonts, random.randint(15, len(fonts)))

    def generate_random_email(self):
        """रैंडम ईमेल जेनरेट करें"""
        domains = [
            'gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com', 'protonmail.com',
            'mail.com', 'icloud.com', 'aol.com', 'zoho.com', 'yandex.com',
            'tutanota.com', 'fastmail.com', 'gmx.com', 'live.com', 'msn.com'
        ]

        prefixes = ['user', 'test', 'demo', 'sample', 'temp', 'new', 'my', 'the']
        suffixes = ['123', '456', '789', '2024', '2023', 'x', 'pro', 'dev']

        if random.choice([True, False]):
            # Simple username
            username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=random.randint(6, 12)))
        else:
            # Prefix + random + suffix
            prefix = random.choice(prefixes)
            middle = ''.join(random.choices(string.ascii_lowercase, k=random.randint(3, 8)))
            suffix = random.choice(suffixes)
            username = f"{prefix}{middle}{suffix}"

        return f"{username}@{random.choice(domains)}"

    def generate_complete_fingerprint(self, device_type='random', browser='chrome'):
        """पूरा फिंगरप्रिंट जेनरेट करें"""
        device_profile = self.get_device_profile(device_type)
        user_agent = self.generate_user_agent(device_profile['type'], browser)
        timezone_data = self.generate_timezone_data()
        language_data = self.generate_language_settings()
        canvas_fp = self.generate_canvas_fingerprint()
        audio_fp = self.generate_audio_fingerprint()
        webrtc_fp = self.generate_webrtc_fingerprint()
        hardware_fp = self.generate_hardware_fingerprint(device_profile)
        network_fp = self.generate_network_fingerprint()
        plugins = self.generate_browser_plugins(device_profile)
        fonts = self.generate_fonts_list(device_profile)

        return {
            'device': device_profile,
            'user_agent': user_agent,
            'timezone': timezone_data,
            'language': language_data,
            'canvas': canvas_fp,
            'audio': audio_fp,
            'webrtc': webrtc_fp,
            'hardware': hardware_fp,
            'network': network_fp,
            'plugins': plugins,
            'fonts': fonts,
            'session_id': str(uuid.uuid4()),
            'generated_at': datetime.now().isoformat()
        }

def run_browser():
    with sync_playwright() as p:
        # Randomize browser fingerprint
        user_agent, platform = random_user_agent()
        screen = random_screen_resolution()
        timezone = random.choice(['America/New_York', 'Europe/London', 'Asia/Kolkata', 'Australia/Sydney'])
        locale = random.choice(['en-US', 'en-GB', 'en-IN', 'en-AU'])
        
        browser = p.chromium.launch(
            headless=False,
            args=[
                f'--user-agent={user_agent}',
                f'--window-size={screen["width"]},{screen["height"]}',
                '--disable-blink-features=AutomationControlled',
                '--disable-infobars',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=IsolateOrigins,site-per-process',
                '--disable-extensions',
                '--disable-popup-blocking',
                f'--lang={locale.split("-")[0]}'
            ]
        )
        
        context = browser.new_context(
            user_agent=user_agent,
            viewport={'width': screen['width'], 'height': screen['height']},
            locale=locale,
            timezone_id=timezone,
            permissions=[],
            color_scheme='light',
            device_scale_factor=random.uniform(0.8, 1.5)
        )  # <-- यहाँ closing parenthesis जोड़ा गया है
        
        page = context.new_page()
        
        try:
            print("Augment वेबसाइट पर जा रहे हैं...")
            page.goto('https://www.augmentcode.com/', timeout=30000)
            page.wait_for_selector('body', timeout=15000)
            
            if "augment" not in page.title().lower():
                print("चेतावनी: हो सकता है Augment की वेबसाइट न लोड हुई हो")
            else:
                print("Augment वेबसाइट सफलतापूर्वक लोड हुई")
                
        except Exception as e:
            print(f"त्रुटि: {str(e)}")
            print("आप मैन्युअली ब्राउज़र का उपयोग कर सकते हैं")
        
        print("\nब्राउज़र फिंगरप्रिंट विवरण:")
        print(f"यूजर एजेंट: {user_agent}")
        print(f"प्लेटफॉर्म: {platform}")
        print(f"रिज़ॉल्यूशन: {screen['width']}x{screen['height']}")
        print(f"टाइमजोन: {timezone}")
        print(f"भाषा: {locale}\n")
        
        print("अब आप मैन्युअली:")
        print("1. साइनअप पेज पर जा सकते हैं")
        print("2. नया अकाउंट बना सकते हैं")
        print("3. एक्सटेंशन का उपयोग कर सकते हैं\n")
        
        input("ब्राउज़र बंद करने के लिए एंटर दबाएं...")
        browser.close()

if __name__ == "__main__":
    run_browser()